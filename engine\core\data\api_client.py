# engine\core\data\api_client.py

import pandas as pd
from openalgo import api
from datetime import datetime, timedelta

class APIClient:
    def __init__(self, config):
        self.config = config
        self.symbol = config["symbol"]
        self.exchange = config["exchange"]
        self.client = api(api_key=config["api_key"],
                          host=config["host"],
                          ws_url=config["ws_url"])
        
    def orderstatus(self, order_id):  
        return self.client.orderstatus(orderid=order_id)    

    async def get_ohlcv(self):
        now = datetime.now()
        df = self.client.history(symbol=self.symbol,
                                 exchange=self.exchange,
                                 interval=self.config["timeframe"],
                                 start_date=(now - timedelta(hours=2)).strftime("%Y-%m-%d"),
                                 end_date=now.strftime("%Y-%m-%d"))
        return df

    def get_current_ltp(self):
        ltp_data = self.client.ltp()
        return float(ltp_data.get(self.symbol, {}).get("ltp", 0))

    async def get_ltp_event(self):
        return self.get_current_ltp()

    def place_order(self, side, quantity):
        return self.client.placeorder(
            strategy="MODULAR_ENGINE",
            symbol=self.symbol,
            exchange=self.exchange,
            action=side,
            price_type=self.config["price_type"],
            product=self.config["product"],
            quantity=quantity
        )

    def funds(self):
        return self.client.funds()
    
   

