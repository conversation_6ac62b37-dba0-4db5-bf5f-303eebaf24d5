# engine\core\data\api_client.py

import pandas as pd
from openalgo import api
from datetime import datetime, timedelta

class APIClient:
    def __init__(self, config):
        import logging
        self.config = config
        self.symbol = config["symbol"]
        self.exchange = config["exchange"]
        self.logger = logging.getLogger("APIClient")

        # Support both old and new configuration formats
        broker_config = config.get("broker_config", {})
        api_key = broker_config.get("openalgo_api_key") or config.get("api_key")
        host = broker_config.get("openalgo_host") or config.get("host")
        ws_url = config.get("ws_url", "ws://127.0.0.1:5000/ws")  # Default WebSocket URL

        if not api_key:
            raise ValueError("API key not found in configuration. Please check 'broker_config.openalgo_api_key' or 'api_key' field.")
        if not host:
            raise ValueError("Host not found in configuration. Please check 'broker_config.openalgo_host' or 'host' field.")

        self.client = api(api_key=api_key, host=host, ws_url=ws_url)
        
    def orderstatus(self, order_id):  
        return self.client.orderstatus(orderid=order_id)    

    async def get_ohlcv(self):
        now = datetime.now()
        df = self.client.history(symbol=self.symbol,
                                 exchange=self.exchange,
                                 interval=self.config["timeframe"],
                                 start_date=(now - timedelta(hours=2)).strftime("%Y-%m-%d"),
                                 end_date=now.strftime("%Y-%m-%d"))

        # Data validation
        if hasattr(df, 'columns') and not df.empty:
            self.logger.debug(f"Fetched {len(df)} rows of {self.symbol} data")
        elif df.empty:
            self.logger.warning(f"No data received for {self.symbol}")
        else:
            self.logger.error(f"Invalid data format received: {type(df)}")

        return df

    def get_current_ltp(self):
        try:
            # Try to get LTP from OpenAlgo API
            ltp_data = self.client.ltp()
            return float(ltp_data.get(self.symbol, {}).get("ltp", 0))
        except (AttributeError, Exception) as e:
            # Fallback: use close price from latest OHLCV data
            self.logger.warning(f"LTP method not available, using close price fallback")
            try:
                # Get the most recent data synchronously
                from datetime import datetime, timedelta
                now = datetime.now()
                df = self.client.history(symbol=self.symbol,
                                       exchange=self.exchange,
                                       interval=self.config["timeframe"],
                                       start_date=(now - timedelta(hours=1)).strftime("%Y-%m-%d"),
                                       end_date=now.strftime("%Y-%m-%d"))

                if hasattr(df, 'empty') and not df.empty:
                    # Reset index to make timestamp a column
                    df = df.reset_index()
                    return float(df['close'].iloc[-1])
                else:
                    self.logger.warning("No data available for LTP fallback")
                    return 20.0  # Default fallback price for testing
            except Exception as e2:
                self.logger.error(f"Failed to get close price: {e2}")
                return 20.0  # Default fallback price for testing

    async def get_ltp_event(self):
        return self.get_current_ltp()

    def place_order(self, side, quantity):
        return self.client.placeorder(
            strategy="MODULAR_ENGINE",
            symbol=self.symbol,
            exchange=self.exchange,
            action=side,
            price_type=self.config["price_type"],
            product=self.config["product"],
            quantity=quantity
        )

    def funds(self):
        return self.client.funds()
    
   

