import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
from typing import Dict, Any, Optional
import logging
from collections import deque
from engine.utility.resource_monitor import resource_monitor, profile_function

class OptimizedDataHandler:
    """
    Memory and CPU optimized data handler for server environments
    """
    
    def __init__(self, symbol: str, max_cache_size: int = 500, timeframe: str = "1m"):
        self.symbol = symbol
        self.timeframe = timeframe
        self.max_cache_size = max_cache_size
        self.logger = logging.getLogger(f"OptimizedDataHandler-{symbol}-{timeframe}")

        # Efficient data storage using deque for fixed-size cache
        self.ohlcv_cache = deque(maxlen=max_cache_size)
        self.last_fetch_time = 0

        # Timeframe-based fetch intervals for optimal performance
        self.fetch_interval = self._get_fetch_interval_for_timeframe(timeframe)

        # LTP cache
        self.ltp_cache = {"value": 0.0, "timestamp": 0}
        self.ltp_cache_duration = 5  # Cache LTP for 5 seconds

        # Pre-allocated arrays for indicators (memory efficient)
        self.indicator_cache = {}

        self.logger.info(f"Initialized data handler for {symbol} on {timeframe} timeframe (fetch interval: {self.fetch_interval}s)")

    def _get_fetch_interval_for_timeframe(self, timeframe: str) -> int:
        """
        Get optimal fetch interval based on timeframe to balance performance and data freshness
        """
        timeframe_intervals = {
            "1m": 10,   # 10 seconds for 1-minute data (frequent updates)
            "3m": 20,   # 20 seconds for 3-minute data
            "5m": 30,   # 30 seconds for 5-minute data
            "15m": 60,  # 1 minute for 15-minute data
            "30m": 120, # 2 minutes for 30-minute data
            "1h": 300,  # 5 minutes for 1-hour data
            "1d": 600   # 10 minutes for daily data
        }

        return timeframe_intervals.get(timeframe, 10)
        
    @profile_function("fetch_ohlcv")
    async def get_optimized_ohlcv(self, client, force_refresh: bool = False) -> pd.DataFrame:
        """
        Get OHLCV data with intelligent caching
        """
        current_time = time.time()
        
        # Check if we need to fetch new data
        if not force_refresh and (current_time - self.last_fetch_time) < self.fetch_interval:
            if self.ohlcv_cache:
                # Return cached data as DataFrame
                return self._cache_to_dataframe()
        
        try:
            # Fetch new data
            resource_monitor.increment_data_fetches()
            df = await client.get_ohlcv()
            
            if not df.empty:
                # Store only essential columns to save memory
                essential_columns = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
                df_filtered = df[essential_columns].copy()
                
                # Convert to more memory-efficient dtypes
                df_filtered = self._optimize_dtypes(df_filtered)
                
                # Update cache efficiently
                self._update_cache(df_filtered)
                self.last_fetch_time = current_time
                
                return df_filtered
            else:
                # Return cached data if fetch failed
                return self._cache_to_dataframe()
                
        except Exception as e:
            self.logger.error(f"Failed to fetch OHLCV data: {e}")
            return self._cache_to_dataframe()
    
    def _optimize_dtypes(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Optimize DataFrame dtypes to reduce memory usage
        """
        # Use float32 instead of float64 for price data (sufficient precision)
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in df.columns:
                df[col] = df[col].astype(np.float32)
        
        # Use int32 for volume (sufficient for most cases)
        if 'volume' in df.columns:
            df['volume'] = df['volume'].astype(np.int32)
        
        return df
    
    def _update_cache(self, df: pd.DataFrame):
        """
        Update cache with new data efficiently
        """
        # Convert DataFrame rows to tuples for efficient storage
        for _, row in df.iterrows():
            data_tuple = (
                row['open'], row['high'], row['low'], 
                row['close'], row['volume'], row['timestamp']
            )
            self.ohlcv_cache.append(data_tuple)
    
    def _cache_to_dataframe(self) -> pd.DataFrame:
        """
        Convert cache to DataFrame efficiently
        """
        if not self.ohlcv_cache:
            return pd.DataFrame()
        
        # Convert deque to DataFrame
        columns = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
        df = pd.DataFrame(list(self.ohlcv_cache), columns=columns)
        
        # Optimize dtypes
        return self._optimize_dtypes(df)
    
    @profile_function("get_ltp")
    def get_cached_ltp(self, client) -> float:
        """
        Get LTP with caching to reduce API calls
        """
        current_time = time.time()
        
        # Check cache validity
        if (current_time - self.ltp_cache["timestamp"]) < self.ltp_cache_duration:
            return self.ltp_cache["value"]
        
        try:
            # Fetch new LTP
            resource_monitor.increment_api_calls()
            ltp = client.get_current_ltp()
            
            # Update cache
            self.ltp_cache = {
                "value": ltp,
                "timestamp": current_time
            }
            
            return ltp
            
        except Exception as e:
            self.logger.error(f"Failed to get LTP: {e}")
            return self.ltp_cache["value"]  # Return cached value on error
    
    @profile_function("calculate_indicators")
    def calculate_indicators_optimized(self, df: pd.DataFrame, fast_period: int = 5, slow_period: int = 20) -> pd.DataFrame:
        """
        Optimized indicator calculation using numpy for speed
        """
        if df.empty or len(df) < slow_period:
            return df
        
        resource_monitor.increment_indicator_calcs()
        
        # Use numpy for faster calculations
        close_prices = df['close'].values.astype(np.float32)
        
        # Calculate EMAs using optimized numpy operations
        ema_fast = self._calculate_ema_optimized(close_prices, fast_period)
        ema_slow = self._calculate_ema_optimized(close_prices, slow_period)
        
        # Add indicators to DataFrame efficiently
        df = df.copy()  # Avoid modifying original
        df['EMA_fast'] = ema_fast
        df['EMA_slow'] = ema_slow
        
        return df
    
    def _calculate_ema_optimized(self, prices: np.ndarray, period: int) -> np.ndarray:
        """
        Optimized EMA calculation using numpy
        """
        alpha = 2.0 / (period + 1)
        ema = np.zeros_like(prices, dtype=np.float32)
        
        # Initialize first value
        ema[0] = prices[0]
        
        # Vectorized EMA calculation
        for i in range(1, len(prices)):
            ema[i] = alpha * prices[i] + (1 - alpha) * ema[i-1]
        
        return ema
    
    def cleanup_cache(self):
        """
        Clean up cache to free memory
        """
        # Keep only recent data
        if len(self.ohlcv_cache) > self.max_cache_size // 2:
            # Remove older half of cache
            for _ in range(len(self.ohlcv_cache) // 2):
                self.ohlcv_cache.popleft()
        
        # Clear indicator cache
        self.indicator_cache.clear()
        
        self.logger.info(f"Cache cleaned up for {self.symbol}")
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """
        Get memory usage statistics
        """
        cache_size = len(self.ohlcv_cache)
        estimated_memory_mb = (cache_size * 6 * 4) / (1024 * 1024)  # 6 floats * 4 bytes each
        
        return {
            "symbol": self.symbol,
            "cache_size": cache_size,
            "max_cache_size": self.max_cache_size,
            "estimated_memory_mb": round(estimated_memory_mb, 3),
            "ltp_cache_age": time.time() - self.ltp_cache["timestamp"]
        }

class DataHandlerPool:
    """
    Pool of optimized data handlers for multiple symbols
    """
    
    def __init__(self, max_handlers: int = 10):
        self.handlers: Dict[str, OptimizedDataHandler] = {}
        self.max_handlers = max_handlers
        self.logger = logging.getLogger("DataHandlerPool")
    
    def get_handler(self, symbol: str, timeframe: str = "1m") -> OptimizedDataHandler:
        """
        Get or create optimized data handler for symbol with timeframe
        """
        handler_key = f"{symbol}_{timeframe}"

        if handler_key not in self.handlers:
            if len(self.handlers) >= self.max_handlers:
                # Remove least recently used handler
                oldest_key = min(self.handlers.keys(),
                               key=lambda k: self.handlers[k].last_fetch_time)
                del self.handlers[oldest_key]
                self.logger.info(f"Removed handler for {oldest_key} due to pool limit")

            self.handlers[handler_key] = OptimizedDataHandler(symbol, timeframe=timeframe)
            self.logger.info(f"Created new handler for {symbol} on {timeframe}")

        return self.handlers[handler_key]
    
    def cleanup_handler(self, symbol: str):
        """
        Cleanup specific handler for symbol
        """
        if symbol in self.handlers:
            self.handlers[symbol].cleanup_cache()
            del self.handlers[symbol]
            self.logger.info(f"Cleaned up handler for {symbol}")

    def cleanup_all(self):
        """
        Cleanup all handlers
        """
        for handler in self.handlers.values():
            handler.cleanup_cache()

        # Clear all handlers
        self.handlers.clear()

        # Force garbage collection
        resource_monitor.force_gc()

        self.logger.info("Cleaned up all handlers")
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """
        Get statistics for all handlers in pool
        """
        stats = {
            "total_handlers": len(self.handlers),
            "max_handlers": self.max_handlers,
            "handlers": {}
        }
        
        total_memory = 0
        for symbol, handler in self.handlers.items():
            handler_stats = handler.get_memory_usage()
            stats["handlers"][symbol] = handler_stats
            total_memory += handler_stats["estimated_memory_mb"]
        
        stats["total_estimated_memory_mb"] = round(total_memory, 3)
        
        return stats

# Global pool instance
data_handler_pool = DataHandlerPool()
