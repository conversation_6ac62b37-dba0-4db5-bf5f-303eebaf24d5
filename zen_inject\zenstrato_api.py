from flask import Blueprint, request, jsonify, current_app
import os
import re

zen_bp = Blueprint("zenstrato", __name__, url_prefix="/zenstrato")

ENV_PATH = os.path.join(".", "TradePilot", ".env")
KEY_NAME = "ZENSTRATO_API_KEY"

def get_env_key():
    if not os.path.exists(ENV_PATH):
        return None
    with open(ENV_PATH) as f:
        for line in f:
            if re.match(rf"^{KEY_NAME}\s*=", line):
                return line.split("=", 1)[1].strip()
    return None

def auth_required(func):
    def wrapper(*args, **kwargs):
        expected = get_env_key()
        received = request.headers.get("X-API-KEY")
        print(f"🔑 [ZEN] Expected: {expected!r}, Received: {received!r}")
        if not expected or received != expected:
            print("❌ [ZEN] Unauthorized access!")
            return jsonify({"error": "Unauthorized"}), 401
        print("✅ [ZEN] Authenticated successfully")
        return func(*args, **kwargs)
    wrapper.__name__ = func.__name__
    return wrapper


@zen_bp.route("/mode", methods=["GET"])
@auth_required
def get_mode():
    print("🔍 [ZEN] get_mode called once authentication passed")
    mode_val = current_app.config.get("CURRENT_MODE", None)

    if mode_val is None:
        return jsonify({"mode": "unknown"})

    if mode_val == 1:
        return jsonify({"mode": "api-analyser"})
    elif mode_val == 0:
        return jsonify({"mode": "live"})
    else:
        return jsonify({"mode": "unknown"})


@zen_bp.route("/command", methods=["POST"])
@auth_required
def handle_command():
    cmd = request.json.get("action")
    print(f"⚙️ [ZEN] Command received: {cmd}")
    if cmd == "refresh":
        return jsonify({"result": "refreshed"}), 200
    elif cmd == "shutdown":
        os._exit(0)
    return jsonify({"error": "Invalid command"}), 400

@zen_bp.route("/status", methods=["GET"])
@auth_required
def status():
    print("📊 [ZEN] Status check triggered")
    return jsonify({
        "broker": "connected",
        "strategy": "idle"
    })

@zen_bp.route("/health", methods=["GET"])
@auth_required
def health_check():
    """Get overall system health"""
    print("🏥 [ZEN] Health check requested")
    try:
        from engine.utility.strategy_health_monitor import health_monitor
        health_data = health_monitor.get_all_strategies_health()

        return jsonify({
            "status": "healthy",
            "timestamp": health_data.get("_summary", {}).get("timestamp"),
            "strategies": health_data
        })
    except Exception as e:
        print(f"❌ [ZEN] Health check failed: {e}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@zen_bp.route("/strategy/<strategy_name>/health", methods=["GET"])
@auth_required
def strategy_health(strategy_name):
    """Get health status for a specific strategy"""
    print(f"🔍 [ZEN] Health check for strategy: {strategy_name}")
    try:
        from engine.utility.strategy_health_monitor import health_monitor
        health_data = health_monitor.get_strategy_health(strategy_name)

        if health_data:
            return jsonify({
                "status": "found",
                "strategy": health_data
            })
        else:
            return jsonify({
                "status": "not_found",
                "message": f"Strategy '{strategy_name}' not found"
            }), 404

    except Exception as e:
        print(f"❌ [ZEN] Strategy health check failed: {e}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@zen_bp.route("/strategies", methods=["GET"])
@auth_required
def list_strategies():
    """List all available strategies"""
    print("📋 [ZEN] Listing strategies")
    try:
        import os
        strategy_dir = os.path.join(".", "strategy")
        config_dir = os.path.join(".", "config")

        strategies = []
        if os.path.exists(strategy_dir):
            for file in os.listdir(strategy_dir):
                if file.endswith(".py") and not file.startswith("__"):
                    strategy_name = file[:-3]  # Remove .py extension
                    config_file = os.path.join(config_dir, f"{strategy_name}.json")

                    strategy_info = {
                        "name": strategy_name,
                        "has_config": os.path.exists(config_file),
                        "strategy_file": file
                    }

                    # Add config info if available
                    if strategy_info["has_config"]:
                        try:
                            import json
                            with open(config_file, 'r') as f:
                                config = json.load(f)
                                strategy_info["config"] = {
                                    "symbol": config.get("symbol"),
                                    "exchange": config.get("exchange"),
                                    "use_openalgo": config.get("use_openalgo", False),
                                    "openalgo_host": config.get("openalgo_host")
                                }
                        except Exception as e:
                            strategy_info["config_error"] = str(e)

                    strategies.append(strategy_info)

        return jsonify({
            "status": "success",
            "strategies": strategies,
            "count": len(strategies)
        })

    except Exception as e:
        print(f"❌ [ZEN] Failed to list strategies: {e}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@zen_bp.route("/openalgo/test", methods=["POST"])
@auth_required
def test_openalgo_connection():
    """Test OpenAlgo API connection"""
    print("🔗 [ZEN] Testing OpenAlgo connection")
    try:
        data = request.json or {}
        openalgo_host = data.get("openalgo_host", "http://127.0.0.1:5000")
        openalgo_api_key = data.get("openalgo_api_key")

        if not openalgo_api_key:
            return jsonify({
                "status": "error",
                "message": "openalgo_api_key is required"
            }), 400

        # Create a test config
        test_config = {
            "symbol": "NSE:INFY",
            "exchange": "NSE",
            "openalgo_host": openalgo_host,
            "openalgo_api_key": openalgo_api_key,
            "use_openalgo": True
        }

        # Test the connection
        from engine.core.data.openalgo_client import OpenAlgoClient
        client = OpenAlgoClient(test_config)
        health_result = client.health_check()

        return jsonify({
            "status": "success",
            "openalgo_test": health_result,
            "host": openalgo_host
        })

    except Exception as e:
        print(f"❌ [ZEN] OpenAlgo connection test failed: {e}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@zen_bp.route("/strategy/<strategy_name>/start", methods=["POST"])
@auth_required
def start_strategy_endpoint(strategy_name):
    """Start a specific strategy"""
    print(f"▶️ [ZEN] Start strategy request: {strategy_name}")
    try:
        # This would integrate with your main strategy runner
        # For now, just register it with health monitor
        from engine.utility.strategy_health_monitor import health_monitor

        # Load strategy config
        import os
        import json
        config_file = os.path.join(".", "config", f"{strategy_name}.json")

        if not os.path.exists(config_file):
            return jsonify({
                "status": "error",
                "message": f"Config file not found for strategy: {strategy_name}"
            }), 404

        with open(config_file, 'r') as f:
            config = json.load(f)

        health_monitor.register_strategy(strategy_name, config)
        health_monitor.start_strategy(strategy_name)

        return jsonify({
            "status": "success",
            "message": f"Strategy '{strategy_name}' start request processed",
            "strategy": strategy_name
        })

    except Exception as e:
        print(f"❌ [ZEN] Failed to start strategy: {e}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@zen_bp.route("/strategy/<strategy_name>/stop", methods=["POST"])
@auth_required
def stop_strategy_endpoint(strategy_name):
    """Stop a specific strategy"""
    print(f"⏹️ [ZEN] Stop strategy request: {strategy_name}")
    try:
        from engine.utility.strategy_health_monitor import health_monitor

        data = request.json or {}
        reason = data.get("reason", "manual_stop")

        health_monitor.stop_strategy(strategy_name, reason)

        return jsonify({
            "status": "success",
            "message": f"Strategy '{strategy_name}' stop request processed",
            "strategy": strategy_name,
            "reason": reason
        })

    except Exception as e:
        print(f"❌ [ZEN] Failed to stop strategy: {e}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500
