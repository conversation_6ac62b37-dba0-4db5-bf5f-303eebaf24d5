import os
import requests
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables from .env (if not already loaded elsewhere)
load_dotenv()

def notify_zenstrato(event_type, payload):
    zen_url = os.getenv("ZENSTRATO_ENGINE_URL", "http://127.0.0.1:5400")
    api_key = os.getenv("ZENSTRATO_API_KEY")

    if not api_key:
        print("❌ Missing ZENSTRATO_API_KEY in .env")
        return

    headers = {"X-API-KEY": api_key}
    endpoint = f"{zen_url}/zenstrato/event"

    data = {
        "event": event_type,
        "payload": payload,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }

    try:
        res = requests.post(endpoint, json=data, headers=headers, timeout=5)
        res.raise_for_status()
        print(f"✅ Event '{event_type}' sent to ZenStrato")
    except Exception as e:
        print(f"❌ Failed to notify ZenStrato: {e}")
