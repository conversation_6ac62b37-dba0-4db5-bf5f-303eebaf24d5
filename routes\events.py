# routes/events.py
from flask import Blueprint, request, jsonify
import time

events_bp = Blueprint("events_bp", __name__)
event_log = []

@events_bp.route("/event", methods=["POST"])
def receive_event():
    data = request.json
    data["timestamp"] = time.time()
    event_log.append(data)
    return jsonify({"status": "received"})

@events_bp.route("/events", methods=["GET"])
def get_events():
    return jsonify(list(reversed(event_log[-100:])))
