import os
import subprocess
import shutil
import sys
import importlib.util
from engine.utility.zen_api_manager import refresh_api_key

TP_REPO = "https://github.com/bvsaisujith/TradePilot.git"

ZEN_INJECT_MAP = {
    "zenstrato_api.py": "",
    "zenstrato_notifier.py": "utils/",
    "settings.py": "blueprints/settings.py",
}


def clone_tradepilot_repo(tp_folder):
    if not os.path.exists(tp_folder):
        print(f"📥 Cloning TradePilot into {tp_folder} ...")
        try:
            subprocess.run(["git", "clone", TP_REPO, tp_folder], check=True)
            print("✅ TradePilot cloned successfully.")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to clone TradePilot repo: {e}")
            exit(1)

def dependencies_installed(pip_path, req_file):
    try:
        result = subprocess.run(
            [pip_path, "install", "-r", req_file, "--dry-run"],
            capture_output=True,
            text=True
        )
        return "Would install" not in result.stdout
    except Exception as e:
        print(f"⚠️ Could not verify dependencies from {req_file}: {e}")
        return False

def install_dependencies(pip_path, requirements):
    for req in requirements:
        if os.path.exists(req):
            if dependencies_installed(pip_path, req):
                print(f"✅ Dependencies already satisfied in {req}")
            else:
                print(f"📦 Installing dependencies from {req}...")
                try:
                    subprocess.run([pip_path, "install", "-r", req], check=True)
                    print(f"✅ Installed dependencies from {req}")
                except subprocess.CalledProcessError as e:
                    print(f"❌ Failed to install dependencies from {req}: {e}")
        else:
            print(f"⚠️ File not found: {req}")

def insert_blueprint_after_master(lines):
    for i, line in enumerate(lines):
        if "app.register_blueprint(master_contract_status_bp)" in line:
            indent = line[:len(line) - len(line.lstrip())]
            zen_register = f"{indent}app.register_blueprint(zen_bp)\n"
            if zen_register.strip() not in "".join(lines):
                lines.insert(i + 1, zen_register)
                print("✅ Inserted zen_bp registration after master_contract_status_bp")
            break
    return lines

def patch_app_py(app_path):
    with open(app_path, "r") as f:
        lines = f.readlines()

    if not any("from zenstrato_api import zen_bp" in l for l in lines):
        for i, line in enumerate(lines):
            if "from flask" in line:
                lines.insert(i + 1, "from zenstrato_api import zen_bp\n")
                print("✅ Inserted zen_bp import")
                break

    lines = insert_blueprint_after_master(lines)

    with open(app_path, "w") as f:
        f.writelines(lines)

    print("✅ Patched app.py to register ZenStrato blueprint")

def inject_zen_modules(base_folder="TradePilot"):
    print("\n🔁 Injecting ZenStrato modules into TradePilot...")
    inject_root = os.path.join(os.getcwd(), "zen_inject")

    for source, target_rel in ZEN_INJECT_MAP.items():
        source_path = os.path.join(inject_root, source)

        if not os.path.exists(source_path):
            print(f"⚠️ Skipped missing file: {source_path}")
            continue

        if target_rel.endswith(".py"):
            target_path = os.path.join(base_folder, target_rel)
        else:
            target_path = os.path.join(base_folder, target_rel, source)

        os.makedirs(os.path.dirname(target_path), exist_ok=True)
        shutil.copy2(source_path, target_path)
        print(f"✅ Injected {source} → {target_path}")

def setup_tradepilot_env(tp_folder="TradePilot", include_nginx=False):
    print("\n🔧 Running setup_tradepilot_env")
    clone_tradepilot_repo(tp_folder)

    venv_path = os.path.join(tp_folder, "venv")
    env_file = os.path.join(tp_folder, ".env")
    sample_env = os.path.join(tp_folder, ".sample.env")
    app_py_path = os.path.join(tp_folder, "app.py")

    python_venv = os.path.join(venv_path, "Scripts" if os.name == "nt" else "bin", "python" + (".exe" if os.name == "nt" else ""))
    pip_venv = os.path.join(venv_path, "Scripts" if os.name == "nt" else "bin", "pip" + (".exe" if os.name == "nt" else ""))

    if not os.path.exists(venv_path):
        print("📦 Creating virtual environment...")
        subprocess.run([sys.executable, "-m", "venv", venv_path], check=True)
        print("✅ Virtual environment created.")

    reqs = [os.path.join(tp_folder, "requirements.txt")]
    if include_nginx:
        reqs.append(os.path.join(tp_folder, "requirement-nginx.txt"))
    install_dependencies(pip_venv, reqs)

    if not os.path.exists(env_file) and os.path.exists(sample_env):
        print("\n⚙️ Setting up .env from .sample.env")
        broker = input(" Broker (e.g. angelone): ").strip()
        api_key = input(" API Key: ").strip()
        api_secret = input(" API Secret: ").strip()
        callback = f"http://127.0.0.1:5000/{broker}/callback"

        shutil.copy(sample_env, env_file)

        with open(env_file, "r+") as f:
            lines = f.readlines()
            f.seek(0)
            for line in lines:
                if "BROKER_API_KEY" in line:
                    f.write(f"BROKER_API_KEY = '{api_key}'\n")
                elif "BROKER_API_SECRET" in line:
                    f.write(f"BROKER_API_SECRET = '{api_secret}'\n")
                elif "REDIRECT_URL" in line:
                    f.write(f"REDIRECT_URL = '{callback}'\n")
                else:
                    f.write(line)
            f.truncate()
        print("✅ .env created.")

    # Refresh ZenStrato API key
    refresh_api_key()

    # Inject ZenStrato modules
    inject_zen_modules(tp_folder)
    patch_app_py(app_py_path)

    print("🚀 Launching TradePilot app.py ...")
    try:
        subprocess.Popen(
            [python_venv, "app.py"],
            cwd=tp_folder,
            env=os.environ,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == "nt" else 0
        )
        print("✅ TradePilot UI is running.")
    except Exception as e:
        print(f"❌ Failed to start app.py: {e}")

    return venv_path
