import asyncio
import logging
import time
import gc
from datetime import datetime
from typing import Dict, Any, Optional

from ..data.client_factory import get_client
from ..strategy.strategy_loader import load_strategy
from engine.utility.logger_utils import log_trade
from engine.utility.alert_utils import alert, send_alert
from engine.utility.strategy_health_monitor import health_monitor
from engine.utility.resource_monitor import resource_monitor, profile_function
from engine.core.data.optimized_data_handler import data_handler_pool
from engine.core.order_management.enhanced_order_manager import EnhancedOrderManager
from openalgo import api

stop_event = asyncio.Event()
active_trades = []

# Global enhanced order manager instance
_enhanced_order_manager = None

def get_enhanced_order_manager(config: Dict[str, Any]) -> EnhancedOrderManager:
    """Get or create enhanced order manager instance"""
    global _enhanced_order_manager

    if _enhanced_order_manager is None:
        # Enhanced order manager configuration
        order_config = {
            "use_redis": config.get("enhanced_order_management", {}).get("use_redis", False),
            "redis_host": config.get("enhanced_order_management", {}).get("redis_host", "localhost"),
            "redis_port": config.get("enhanced_order_management", {}).get("redis_port", 6379),
            "redis_db": config.get("enhanced_order_management", {}).get("redis_db", 0),
            "max_order_retries": config.get("enhanced_order_management", {}).get("max_order_retries", 3),
            "retry_delay_seconds": config.get("enhanced_order_management", {}).get("retry_delay_seconds", 2),
            "retry_backoff_multiplier": config.get("enhanced_order_management", {}).get("retry_backoff_multiplier", 1.5),
            "max_concurrent_orders": config.get("enhanced_order_management", {}).get("max_concurrent_orders", 5)
        }

        _enhanced_order_manager = EnhancedOrderManager(order_config)
        logging.info("🚀 Enhanced order manager initialized")

    return _enhanced_order_manager

async def handle_trade(strategy_name, strategy, config, client, stop_event_param=None):
    """Enhanced trade handling with retry logic and monitoring"""
    # Use provided stop event or global one
    current_stop_event = stop_event_param if stop_event_param else stop_event
    trade = {}
    order_manager = get_enhanced_order_manager(config)

    try:
        # Get optimized data handler with timeframe
        symbol = config.get("symbol", "")
        timeframe = config.get("timeframe", "1m")
        data_handler = data_handler_pool.get_handler(symbol, timeframe)

        # Get current price efficiently
        entry_price = data_handler.get_cached_ltp(client)
        if entry_price <= 0:
            logging.warning("Invalid LTP received, skipping trade")
            return

        qty = max(1, int(config["capital"] // entry_price))
        sl = entry_price - config["sl_multiplier"]
        tp = entry_price + config["tp_multiplier"]

        # Place order using enhanced order manager
        result = await order_manager.place_order_with_retry(
            client=client,
            order_type="BUY",
            quantity=qty,
            strategy_name=strategy_name,
            symbol=symbol,
            price=entry_price
        )

        if result["status"] != "success":
            print(f"❌ BUY failed: {result.get('error', 'Unknown error')}")
            health_monitor.record_trade(strategy_name, {
                "status": "failed",
                "error": result.get("error", "Order placement failed"),
                "retry_count": result.get("retry_count", 0)
            })
            return

        order_id = result["order_id"]
        tradepilot_order_id = result.get("tradepilot_order_id")

        trade.update({
            "strategy": strategy_name,
            "symbol": config["symbol"],
            "qty": qty,
            "entry_price": entry_price,
            "stop_loss": sl,
            "target": tp,
            "order_id": order_id,
            "tradepilot_order_id": tradepilot_order_id,
            "status": "open",
            "entry_time": str(datetime.now())
        })

        print(f"🟢 Enhanced Entry @ ₹{entry_price} (Order: {tradepilot_order_id})")
        alert(f"🟢 {strategy_name} BUY @ ₹{entry_price} | Enhanced Engine")
        send_alert(config, strategy_name, "BUY", {
            "price": entry_price,
            "qty": qty,
            "stop_loss": sl,
            "target": tp,
            "order_id": tradepilot_order_id,
            "enhanced": True
        })

        health_monitor.record_trade(strategy_name, {
            "status": "success",
            "action": "BUY",
            "order_id": order_id,
            "tradepilot_order_id": tradepilot_order_id,
            "price": entry_price,
            "quantity": qty,
            "enhanced_engine": True
        })

        # Enhanced monitoring loop
        loop_count = 0
        last_cleanup = time.time()

        while True:
            if current_stop_event.is_set():
                print("⚠️ Shutdown. Exiting trade...")
                break

            loop_start = time.time()

            # Get current price efficiently
            ltp = data_handler.get_cached_ltp(client)

            # Get market data with caching
            df = await data_handler.get_optimized_ohlcv(client)

            if not df.empty:
                # Calculate indicators efficiently
                df = strategy.populate_indicators(df)

                # Check exit conditions
                exit_due_to_signal = strategy.exit_signal(df, ltp, entry_price, "BUY")
                exit_due_to_sl = ltp <= sl
                exit_due_to_tp = ltp >= tp

                # Check MIS square-off time (2:50 PM)
                exit_due_to_mis = False
                if config.get("product", "").upper() == "MIS":
                    current_time = datetime.now()
                    if current_time.hour >= 14 and current_time.minute >= 50:
                        exit_due_to_mis = True
                        print("⏰ MIS Square-off: Exiting position at 2:50 PM")

                if exit_due_to_signal or exit_due_to_sl or exit_due_to_tp or exit_due_to_mis:
                    if exit_due_to_mis:
                        print(f"🔔 MIS Exit: Current price ₹{ltp} at 2:50 PM square-off")
                    elif exit_due_to_sl:
                        print(f"🛑 Stop Loss: ₹{ltp} <= ₹{sl}")
                    elif exit_due_to_tp:
                        print(f"🎯 Take Profit: ₹{ltp} >= ₹{tp}")
                    else:
                        print(f"📊 Strategy Exit Signal at ₹{ltp}")
                    break

            # Performance monitoring
            loop_duration = time.time() - loop_start
            if loop_duration > 5.0:
                logging.warning(f"Slow trade monitoring loop: {loop_duration:.2f}s")

            # Periodic cleanup
            current_time = time.time()
            if current_time - last_cleanup > 300:  # Every 5 minutes
                resource_monitor.optimize_memory()
                last_cleanup = current_time

            loop_count += 1
            await asyncio.sleep(2)  # Reduced sleep for better responsiveness

        # Place exit order using enhanced order manager
        exit_result = await order_manager.place_order_with_retry(
            client=client,
            order_type="SELL",
            quantity=qty,
            strategy_name=strategy_name,
            symbol=symbol,
            price=ltp
        )

        if exit_result["status"] == "success":
            exit_order_id = exit_result.get("tradepilot_order_id")
            pnl = (ltp - entry_price) * qty

            trade.update({
                "exit_price": ltp,
                "exit_time": str(datetime.now()),
                "exit_order_id": exit_order_id,
                "pnl": pnl,
                "status": "closed"
            })

            print(f"🔴 Enhanced Exit @ ₹{ltp} (P&L: ₹{pnl:.2f})")
            alert(f"🔴 {strategy_name} SELL @ ₹{ltp}, P&L: ₹{pnl:.2f} | Enhanced Engine")
            send_alert(config, strategy_name, "SELL", {
                "price": ltp,
                "qty": qty,
                "pnl": pnl,
                "order_id": exit_order_id,
                "enhanced": True
            })

            health_monitor.record_trade(strategy_name, {
                "status": "success",
                "action": "SELL",
                "order_id": exit_result["order_id"],
                "tradepilot_order_id": exit_order_id,
                "price": ltp,
                "quantity": qty,
                "pnl": pnl,
                "enhanced_engine": True
            })
        else:
            print(f"❌ Exit order failed: {exit_result.get('error', 'Unknown error')}")
            trade["exit_error"] = exit_result.get("error", "Exit order failed")

    except Exception as e:
        trade["status"] = "error"
        trade["error"] = str(e)
        print(f"❌ Enhanced Trade error: {e}")
        alert(f"⚠️ Enhanced Trade error: {e}")
        health_monitor.record_error(strategy_name, str(e))
    finally:
        if trade:
            log_trade(trade)

async def run_strategy(strategy_name, external_stop_event=None):
    """Enhanced strategy runner with optimized data handling and resource monitoring"""
    strategy, config = load_strategy(strategy_name)

    # Use external stop event if provided, otherwise use global stop event
    current_stop_event = external_stop_event if external_stop_event else stop_event
    client = get_client(config)

    # Initialize enhanced features
    print(f"🚀 Starting Enhanced ZenStrato Engine for {strategy_name}")

    # Get optimized data handler with timeframe
    symbol = config.get("symbol", "")
    timeframe = config.get("timeframe", "1m")
    data_handler = data_handler_pool.get_handler(symbol, timeframe)

    # Initialize enhanced order manager
    order_manager = get_enhanced_order_manager(config)

    # Register strategy with health monitor
    health_monitor.register_strategy(strategy_name, config)
    health_monitor.start_strategy(strategy_name)

    # Performance tracking
    loop_count = 0
    start_time = time.time()
    last_signal_time = 0
    signal_cooldown = config.get("parallel_processing", {}).get("signal_cooldown_seconds", 30)

    try:
        print(f"✅ Enhanced features initialized:")
        print(f"   • Order retry logic: {order_manager.max_retries} attempts")
        print(f"   • Data caching: {data_handler.max_cache_size} rows")
        print(f"   • Signal cooldown: {signal_cooldown}s")
        print(f"   • Resource monitoring: Active")

        while not current_stop_event.is_set():
            loop_start_time = time.time()

            # Enhanced heartbeat with performance metrics
            memory_stats = resource_monitor.get_current_stats()
            health_monitor.heartbeat(strategy_name, {
                "active_trades": len(active_trades),
                "loop_count": loop_count,
                "uptime_seconds": time.time() - start_time,
                "memory_mb": memory_stats["memory"]["current_mb"],
                "last_check": datetime.now().isoformat(),
                "enhanced_engine": True
            })

            # Check OpenAlgo connection if using it
            if config.get("use_openalgo", False):
                try:
                    health_result = client.health_check()
                    health_monitor.update_openalgo_status(strategy_name,
                        "connected" if health_result.get("openalgo_connected") else "disconnected")
                except Exception as e:
                    health_monitor.update_openalgo_status(strategy_name, "error")
                    health_monitor.record_error(strategy_name, f"OpenAlgo health check failed: {e}")

            try:
                # Use optimized data fetching
                df = await data_handler.get_optimized_ohlcv(client)

                if not df.empty:
                    # Calculate indicators efficiently
                    df = strategy.populate_indicators(df)

                    # Check entry signal with cooldown
                    current_time = time.time()
                    if (current_time - last_signal_time) > signal_cooldown:
                        if strategy.entry_signal(df):
                            if config.get("parallel_orders", False):
                                if len(active_trades) < config.get("max_concurrent_trades", 1):
                                    task = asyncio.create_task(handle_trade(strategy_name, strategy, config, client, current_stop_event))
                                    active_trades.append(task)
                                    task.add_done_callback(lambda t: active_trades.remove(t))
                                    last_signal_time = current_time
                            else:
                                if not active_trades:
                                    task = asyncio.create_task(handle_trade(strategy_name, strategy, config, client, current_stop_event))
                                    active_trades.append(task)
                                    task.add_done_callback(lambda t: active_trades.remove(t))
                                    last_signal_time = current_time

            except Exception as e:
                health_monitor.record_error(strategy_name, f"Enhanced strategy execution error: {e}")
                print(f"❌ Enhanced strategy error: {e}")

            # Performance monitoring
            loop_duration = time.time() - loop_start_time
            if loop_duration > 5.0:
                print(f"⚠️ Slow loop detected: {loop_duration:.2f}s")

            # Check MIS exit time (2:50 PM for intraday square-off)
            if config.get("product", "").upper() == "MIS":
                current_time = datetime.now()
                # Check if it's 2:50 PM or later (10 minutes before market close)
                if current_time.hour >= 14 and current_time.minute >= 50:
                    print("⏰ MIS Square-off time reached (2:50 PM) - Exiting all positions")
                    health_monitor.record_trade(strategy_name, {
                        "status": "info",
                        "action": "MIS_SQUARE_OFF",
                        "time": current_time.isoformat(),
                        "reason": "Intraday square-off at 2:50 PM"
                    })
                    break

            # Adaptive sleep based on configuration
            sleep_duration = config.get("parallel_processing", {}).get("loop_interval_seconds", 10)
            await asyncio.sleep(sleep_duration)

            loop_count += 1

            # Periodic resource optimization
            if loop_count % 100 == 0:
                resource_monitor.optimize_memory()
                print(f"🔧 Memory optimized at loop {loop_count}")

    except Exception as e:
        health_monitor.record_error(strategy_name, f"Enhanced strategy runner error: {e}")
        print(f"❌ Enhanced strategy runner error: {e}")
        health_monitor.stop_strategy(strategy_name, f"error: {e}")
        raise
    finally:
        # Wait for active trades to complete
        if active_trades:
            print(f"⏳ Waiting for {len(active_trades)} active trades to complete...")
            await asyncio.gather(*active_trades, return_exceptions=True)

        # Cleanup resources
        data_handler_pool.cleanup_handler(symbol)

        # Final statistics
        total_runtime = time.time() - start_time
        order_stats = order_manager.get_order_statistics()

        print(f"📊 Enhanced Engine Statistics:")
        print(f"   • Total runtime: {total_runtime:.2f}s")
        print(f"   • Loops executed: {loop_count}")
        print(f"   • Orders placed: {order_stats['total_orders']}")
        print(f"   • Success rate: {order_stats['success_rate']:.1f}%")

        health_monitor.stop_strategy(strategy_name, "completed")
        print(f"✅ Enhanced strategy {strategy_name} stopped")
