import pandas as pd
import numpy as np
from engine.core.strategy.istrategy import IStrategy
from engine.utility.resource_monitor import profile_function
import logging

class Strategy(IStrategy):
    """
    Optimized RSI + MACD strategy for YESBANK
    
    Entry Signals:
    - BUY: RSI < 30 (oversold) AND MACD line crosses above signal line
    - SELL: RSI > 70 (overbought) AND MACD line crosses below signal line
    
    Exit Signals:
    - Stop Loss: 2% from entry
    - Take Profit: 4% from entry
    - RSI reversal signals
    """
    
    def __init__(self, config):
        super().__init__(config)
        self.config = config
        self.logger = logging.getLogger("RSIMACDYesBankStrategy")

        # Timeframe configuration - used throughout the strategy
        self.timeframe = config.get("timeframe", "1m")
        self.symbol = config.get("symbol", "UNKNOWN")

        # Strategy parameters (optimized for server resources)
        indicators = config.get("indicators", {})
        self.rsi_period = indicators.get("rsi_period", 14)
        self.rsi_oversold = indicators.get("rsi_oversold", 30)
        self.rsi_overbought = indicators.get("rsi_overbought", 70)

        # MACD parameters
        self.macd_fast = indicators.get("macd_fast", 12)
        self.macd_slow = indicators.get("macd_slow", 26)
        self.macd_signal = indicators.get("macd_signal", 9)

        # Risk management
        self.stop_loss_pct = config.get("sl_multiplier", 2.0)
        self.take_profit_pct = config.get("tp_multiplier", 4.0)

        # Timeframe-based adjustments for indicator periods
        self._adjust_periods_for_timeframe()

        # Memory optimization - store only last few values
        self.last_rsi = None
        self.last_macd = None
        self.last_signal = None

        self.logger.info(f"Strategy initialized for {self.symbol} on {self.timeframe} timeframe")

    def _adjust_periods_for_timeframe(self):
        """
        Adjust indicator periods based on timeframe for optimal performance
        """
        timeframe_multipliers = {
            "1m": 1.0,    # Base timeframe
            "3m": 0.5,    # Faster signals for longer timeframes
            "5m": 0.4,
            "15m": 0.3,
            "30m": 0.25,
            "1h": 0.2,
            "1d": 0.1
        }

        multiplier = timeframe_multipliers.get(self.timeframe, 1.0)

        if multiplier != 1.0:
            # Adjust periods for different timeframes
            self.rsi_period = max(7, int(self.rsi_period * multiplier))
            self.macd_fast = max(6, int(self.macd_fast * multiplier))
            self.macd_slow = max(12, int(self.macd_slow * multiplier))
            self.macd_signal = max(4, int(self.macd_signal * multiplier))

            self.logger.info(f"Adjusted periods for {self.timeframe}: RSI={self.rsi_period}, MACD=({self.macd_fast},{self.macd_slow},{self.macd_signal})")

    @profile_function("populate_indicators")
    def populate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate RSI and MACD indicators efficiently
        """
        if df.empty or len(df) < max(self.rsi_period, self.macd_slow):
            return df
        
        # Work with copy to avoid modifying original
        df = df.copy()
        
        # Calculate RSI using optimized numpy operations
        df['RSI'] = self._calculate_rsi_optimized(df['close'].values)
        
        # Calculate MACD using optimized numpy operations
        macd_line, signal_line, histogram = self._calculate_macd_optimized(df['close'].values)
        df['MACD'] = macd_line
        df['MACD_signal'] = signal_line
        df['MACD_histogram'] = histogram
        
        # Store last values for quick access
        if len(df) > 0:
            self.last_rsi = df['RSI'].iloc[-1]
            self.last_macd = df['MACD'].iloc[-1]
            self.last_signal = df['MACD_signal'].iloc[-1]
        
        return df
    
    def _calculate_rsi_optimized(self, prices: np.ndarray) -> np.ndarray:
        """
        Optimized RSI calculation using numpy
        """
        if len(prices) < self.rsi_period + 1:
            return np.full(len(prices), 50.0, dtype=np.float32)
        
        # Calculate price changes
        deltas = np.diff(prices)
        
        # Separate gains and losses
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        # Calculate initial averages
        avg_gain = np.mean(gains[:self.rsi_period])
        avg_loss = np.mean(losses[:self.rsi_period])
        
        # Initialize RSI array
        rsi = np.full(len(prices), 50.0, dtype=np.float32)
        
        # Calculate RSI for each point
        for i in range(self.rsi_period, len(deltas)):
            # Smoothed averages (Wilder's smoothing)
            avg_gain = (avg_gain * (self.rsi_period - 1) + gains[i]) / self.rsi_period
            avg_loss = (avg_loss * (self.rsi_period - 1) + losses[i]) / self.rsi_period
            
            # Calculate RSI
            if avg_loss == 0:
                rsi[i + 1] = 100.0
            else:
                rs = avg_gain / avg_loss
                rsi[i + 1] = 100.0 - (100.0 / (1.0 + rs))
        
        return rsi
    
    def _calculate_macd_optimized(self, prices: np.ndarray) -> tuple:
        """
        Optimized MACD calculation using numpy
        """
        if len(prices) < self.macd_slow:
            zeros = np.zeros(len(prices), dtype=np.float32)
            return zeros, zeros, zeros
        
        # Calculate EMAs
        ema_fast = self._calculate_ema_optimized(prices, self.macd_fast)
        ema_slow = self._calculate_ema_optimized(prices, self.macd_slow)
        
        # MACD line
        macd_line = ema_fast - ema_slow
        
        # Signal line (EMA of MACD line)
        signal_line = self._calculate_ema_optimized(macd_line, self.macd_signal)
        
        # Histogram
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    def _calculate_ema_optimized(self, prices: np.ndarray, period: int) -> np.ndarray:
        """
        Optimized EMA calculation
        """
        alpha = 2.0 / (period + 1)
        ema = np.zeros_like(prices, dtype=np.float32)
        
        # Initialize with first price
        ema[0] = prices[0]
        
        # Calculate EMA
        for i in range(1, len(prices)):
            ema[i] = alpha * prices[i] + (1 - alpha) * ema[i-1]
        
        return ema
    
    def entry_signal(self, df: pd.DataFrame) -> bool:
        """
        Check for entry signal (optimized for performance)
        """
        if df.empty or len(df) < 2:
            return False
        
        try:
            # Get current and previous values
            current = df.iloc[-1]
            previous = df.iloc[-2]
            
            # Check if we have all required indicators
            required_cols = ['RSI', 'MACD', 'MACD_signal']
            if not all(col in df.columns for col in required_cols):
                return False
            
            current_rsi = current['RSI']
            current_macd = current['MACD']
            current_signal = current['MACD_signal']
            
            prev_macd = previous['MACD']
            prev_signal = previous['MACD_signal']
            
            # BUY Signal: RSI oversold AND MACD bullish crossover
            if (current_rsi < self.rsi_oversold and 
                prev_macd <= prev_signal and 
                current_macd > current_signal):
                
                self.logger.info(f"BUY signal: RSI={current_rsi:.2f}, MACD crossover")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in entry_signal: {e}")
            return False
    
    def exit_signal(self, df: pd.DataFrame, current_price: float, entry_price: float = None, position_type: str = "BUY") -> bool:
        """
        Check for exit signal
        """
        if df.empty or len(df) < 2:
            return False
        
        try:
            current = df.iloc[-1]
            previous = df.iloc[-2]
            
            if 'RSI' not in df.columns:
                return False
            
            current_rsi = current['RSI']
            current_macd = current['MACD']
            current_signal = current['MACD_signal']
            
            prev_macd = previous['MACD']
            prev_signal = previous['MACD_signal']
            
            # Exit conditions for BUY position
            if position_type == "BUY":
                # RSI overbought OR MACD bearish crossover
                if (current_rsi > self.rsi_overbought or 
                    (prev_macd >= prev_signal and current_macd < current_signal)):
                    
                    self.logger.info(f"SELL signal: RSI={current_rsi:.2f}, MACD crossover")
                    return True
            
            # Stop loss / Take profit (if entry price provided)
            if entry_price:
                if position_type == "BUY":
                    stop_loss = entry_price * (1 - self.stop_loss_pct / 100)
                    take_profit = entry_price * (1 + self.take_profit_pct / 100)
                    
                    if current_price <= stop_loss:
                        self.logger.info(f"Stop loss triggered: {current_price} <= {stop_loss}")
                        return True
                    
                    if current_price >= take_profit:
                        self.logger.info(f"Take profit triggered: {current_price} >= {take_profit}")
                        return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in exit_signal: {e}")
            return False
    
    def get_strategy_info(self) -> dict:
        """
        Get strategy information including timeframe configuration
        """
        return {
            "name": "RSI_MACD_YESBANK",
            "symbol": self.symbol,
            "timeframe": self.timeframe,
            "indicators": ["RSI", "MACD"],
            "parameters": {
                "timeframe": self.timeframe,
                "rsi_period": self.rsi_period,
                "rsi_oversold": self.rsi_oversold,
                "rsi_overbought": self.rsi_overbought,
                "macd_fast": self.macd_fast,
                "macd_slow": self.macd_slow,
                "macd_signal": self.macd_signal,
                "stop_loss_pct": self.stop_loss_pct,
                "take_profit_pct": self.take_profit_pct
            },
            "last_values": {
                "rsi": self.last_rsi,
                "macd": self.last_macd,
                "signal": self.last_signal
            },
            "timeframe_info": {
                "configured_timeframe": self.timeframe,
                "data_frequency": f"{self.timeframe} bars",
                "indicator_periods_adjusted": self.timeframe != "1m"
            }
        }
    
    def optimize_for_server(self):
        """
        Server-specific optimizations
        """
        # Reduce memory footprint
        self.last_rsi = None
        self.last_macd = None
        self.last_signal = None
        
        # Force garbage collection
        import gc
        gc.collect()
        
        self.logger.info("Strategy optimized for server resources")
